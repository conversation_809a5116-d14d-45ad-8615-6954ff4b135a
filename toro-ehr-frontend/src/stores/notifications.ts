import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { api } from '@/api'
import type { NotificationResponse, NotificationType } from '@/api/api-reference'
import { useAuthStore } from './auth'

export const useNotificationsStore = defineStore('notifications', () => {
  const notifications = ref<NotificationResponse[]>([])
  const unreadCount = ref(0)
  const isLoading = ref(false)
  const currentPage = ref(1)
  const pageSize = ref(20)
  const totalPages = ref(0)
  const totalItems = ref(0)

  // polling interval reference
  let pollingInterval: NodeJS.Timeout | null = null

  const authStore = useAuthStore()

  // filter notifications based on user role
  const filteredNotifications = computed(() => {
    const userRole = authStore.user?.selectedUserRole

    return notifications.value.filter(notification => {
      if (userRole === 'Patient') {
        // patients only see AppointmentConfirmed notifications
        return notification.notificationType === 'Appointment Confirmed'
      } else if (userRole === 'Employee') {
        // employees see all except AppointmentConfirmed
        return notification.notificationType !== 'Appointment Confirmed'
      }
      return false
    })
  })

  const filteredUnreadCount = computed(() => {
    const userRole = authStore.user?.selectedUserRole

    if (userRole === 'Patient') {
      return notifications.value.filter(n =>
        n.status === 'Unread' && n.notificationType === 'Appointment Confirmed'
      ).length
    } else if (userRole === 'Employee') {
      return notifications.value.filter(n =>
        n.status === 'Unread' && n.notificationType !== 'Appointment Confirmed'
      ).length
    }
    return 0
  })

  async function fetchNotifications(page = 1) {
    try {
      isLoading.value = true
      const response = await api.notifications.notificationGetUserNotifications({
        PageNumber: page,
        PageSize: pageSize.value
      })

      if (response.data) {
        if (page === 1) {
          notifications.value = response.data.items || []
        } else {
          // append for pagination
          notifications.value.push(...(response.data.items || []))
        }

        currentPage.value = response.data.pageNumber || 1
        totalPages.value = response.data.totalPages || 0
        totalItems.value = response.data.totalItems || 0
      }
    } catch (error) {
      console.error('Failed to fetch notifications:', error)
    } finally {
      isLoading.value = false
    }
  }

  async function fetchUnreadCount() {
    try {
      const response = await api.notifications.notificationGetUnreadNotificationCount()
      unreadCount.value = response.data || 0
    } catch (error) {
      console.error('Failed to fetch unread count:', error)
    }
  }

  async function markAsRead(notificationId: string) {
    try {
      await api.notifications.notificationMarkNotificationAsRead(notificationId)

      // update local state
      const notification = notifications.value.find(n => n.id === notificationId)
      if (notification) {
        notification.status = 'Read'
        notification.readAt = new Date().toISOString()
      }

      // update unread count
      await fetchUnreadCount()
    } catch (error) {
      console.error('Failed to mark notification as read:', error)
    }
  }

  async function deleteNotification(notificationId: string) {
    try {
      await api.notifications.notificationDeleteNotification(notificationId)

      // remove from local state
      notifications.value = notifications.value.filter(n => n.id !== notificationId)

      // update unread count
      await fetchUnreadCount()
    } catch (error) {
      console.error('Failed to delete notification:', error)
    }
  }

  function startPolling() {
    // clear existing interval
    if (pollingInterval) {
      clearInterval(pollingInterval)
    }

    // only start polling if user is logged in
    if (!authStore.user) {
      return
    }

    // poll every 30 seconds
    pollingInterval = setInterval(async () => {
      // check if user is still logged in before making API calls
      if (!authStore.user) {
        stopPolling()
        return
      }

      await fetchUnreadCount()
      // refresh first page to get latest notifications
      if (currentPage.value === 1) {
        await fetchNotifications(1)
      }
    }, 30000)
  }

  function stopPolling() {
    if (pollingInterval) {
      clearInterval(pollingInterval)
      pollingInterval = null
    }
  }

  async function loadMore() {
    if (currentPage.value < totalPages.value && !isLoading.value) {
      await fetchNotifications(currentPage.value + 1)
    }
  }

  function reset() {
    notifications.value = []
    unreadCount.value = 0
    currentPage.value = 1
    totalPages.value = 0
    totalItems.value = 0
    stopPolling()
  }

  return {
    notifications: filteredNotifications,
    unreadCount: filteredUnreadCount,
    isLoading,
    currentPage,
    pageSize,
    totalPages,
    totalItems,
    fetchNotifications,
    fetchUnreadCount,
    markAsRead,
    deleteNotification,
    startPolling,
    stopPolling,
    loadMore,
    reset
  }
})
