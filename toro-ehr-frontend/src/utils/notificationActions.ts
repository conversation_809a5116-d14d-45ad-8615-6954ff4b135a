import type { NotificationResponse, NotificationType } from '@/api/api-reference'
import { useRouter } from 'vue-router'
import { api } from '@/api'
import { useToast } from 'vue-toastification'

export interface NotificationAction {
  label: string
  icon: string
  variant: 'primary' | 'secondary' | 'success' | 'danger' | 'warning'
  action: () => Promise<void> | void
}

export function getNotificationActions(notification: NotificationResponse): NotificationAction[] {
  const router = useRouter()
  const toast = useToast()

  const actions: NotificationAction[] = []

  switch (notification.notificationType) {
    case 'Appointment Confirmed':
      // for patients - view appointment details
      actions.push({
        label: 'View',
        icon: 'pi pi-eye',
        variant: 'primary',
        action: () => {
          if (notification.relatedEntityId) {
            router.push(`/patient-appointments`)
          }
        }
      })
      break

    case 'Appointment Requested':
      // for employees - confirm or cancel appointment
      actions.push({
        label: 'Confirm',
        icon: 'pi pi-check',
        variant: 'success',
        action: async () => {
          try {
            if (notification.relatedEntityId) {
              // call confirm appointment API
              await api.appointments.appointmentConfirm({
                id: notification.relatedEntityId,
                timestamp: new Date().toISOString()
              })
              toast.success('Appointment confirmed successfully')

              // note: the notification should be updated/removed by the backend
              // the frontend will refresh to get the updated state
            }
          } catch (error) {
            toast.error('Failed to confirm appointment')
            throw error // re-throw to handle in component
          }
        }
      })

      actions.push({
        label: 'Cancel',
        icon: 'pi pi-times',
        variant: 'danger',
        action: async () => {
          try {
            if (notification.relatedEntityId) {
              // call cancel appointment API
              await api.appointments.appointmentCancelAppointment({
                id: notification.relatedEntityId,
                timestamp: new Date().toISOString()
              })
              toast.success('Appointment cancelled successfully')

              // note: the notification should be updated/removed by the backend
              // the frontend will refresh to get the updated state
            }
          } catch (error) {
            toast.error('Failed to cancel appointment')
            throw error // re-throw to handle in component
          }
        }
      })

      actions.push({
        label: 'View Details',
        icon: 'pi pi-info-circle',
        variant: 'secondary',
        action: () => {
          router.push('/appointments')
        }
      })
      break

    case 'Appointment Rescheduled':
      // for employees - view updated appointment
      actions.push({
        label: 'View',
        icon: 'pi pi-calendar',
        variant: 'primary',
        action: () => {
          router.push('/appointments')
        }
      })
      break

    case 'Appointment Missed':
      // for employees - view missed appointment, maybe reschedule
      actions.push({
        label: 'View',
        icon: 'pi pi-exclamation-triangle',
        variant: 'warning',
        action: () => {
          router.push('/appointments')
        }
      })

      actions.push({
        label: 'Reschedule',
        icon: 'pi pi-calendar-plus',
        variant: 'secondary',
        action: () => {
          if (notification.relatedEntityId) {
            router.push(`/appointments`)
          }
        }
      })
      break

    default:
      // default action - just view
      actions.push({
        label: 'View',
        icon: 'pi pi-eye',
        variant: 'primary',
        action: () => {
          // navigate to appropriate page based on user role
          const userRole = localStorage.getItem('user') ?
            JSON.parse(localStorage.getItem('user')!).selectedUserRole : null

          if (userRole === 'Patient') {
            router.push('/patient-appointments')
          } else {
            router.push('/appointments')
          }
        }
      })
      break
  }

  return actions
}

export function getNotificationTypeColor(type: NotificationType): string {
  switch (type) {
    case 'Appointment Confirmed':
      return 'text-green-600 bg-green-100'
    case 'Appointment Requested':
      return 'text-blue-600 bg-blue-100'
    case 'Appointment Rescheduled':
      return 'text-yellow-600 bg-yellow-100'
    case 'Appointment Missed':
      return 'text-red-600 bg-red-100'
    default:
      return 'text-gray-600 bg-gray-100'
  }
}

export function getNotificationTypeIcon(type: NotificationType): string {
  switch (type) {
    case 'Appointment Confirmed':
      return 'pi pi-check-circle'
    case 'Appointment Requested':
      return 'pi pi-clock'
    case 'Appointment Rescheduled':
      return 'pi pi-calendar'
    case 'Appointment Missed':
      return 'pi pi-exclamation-triangle'
    default:
      return 'pi pi-info-circle'
  }
}
