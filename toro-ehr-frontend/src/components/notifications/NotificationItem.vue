<template>
  <div
    class="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow duration-200"
    :class="{ 'border-l-4 border-l-blue-500 bg-blue-50': notification.status === 'Unread' }"
  >
    <div class="flex items-start space-x-4">
      <!-- Notification icon -->
      <div
        class="flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center"
        :class="getNotificationTypeColor(notification.notificationType!)"
      >
        <i :class="getNotificationTypeIcon(notification.notificationType!)" class="text-lg"></i>
      </div>

      <!-- Notification content -->
      <div class="flex-1 min-w-0">
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <h3 class="text-sm font-semibold text-gray-900 mb-1">
              {{ notification.title }}
            </h3>
            <p class="text-sm text-gray-600 mb-2">
              {{ notification.message }}
            </p>
            <div class="flex items-center space-x-4 text-xs text-gray-500">
              <span>{{ formatDate(notification.createdAt) }}</span>
              <span
                v-if="notification.status === 'Unread'"
                class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
              >
                <div class="w-1.5 h-1.5 bg-blue-500 rounded-full mr-1"></div>
                Unread
              </span>
              <span
                v-else
                class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600"
              >
                Read
              </span>
            </div>
          </div>

          <!-- Status toggle and delete buttons -->
          <div class="flex items-center space-x-2 ml-4">
            <button
              @click="toggleReadStatus"
              :disabled="isUpdating"
              class="p-1.5 text-gray-400 hover:text-gray-600 transition-colors duration-200"
              :title="notification.status === 'Unread' ? 'Mark as read' : 'Mark as unread'"
            >
              <i
                :class="notification.status === 'Unread' ? 'pi pi-eye' : 'pi pi-eye-slash'"
                class="text-sm"
              ></i>
            </button>

            <button
              @click="deleteNotification"
              :disabled="isDeleting"
              class="p-1.5 text-gray-400 hover:text-red-600 transition-colors duration-200"
              title="Delete notification"
            >
              <i class="pi pi-trash text-sm"></i>
            </button>
          </div>
        </div>

        <!-- Action buttons -->
        <div v-if="actions.length > 0" class="mt-3 flex flex-wrap gap-2">
          <Button
            v-for="action in actions"
            :key="action.label"
            :label="action.label"
            :icon="action.icon"
            :severity="getButtonSeverity(action.variant)"
            size="small"
            @click="handleActionClick(action)"
            :disabled="isActionLoading"
            class="text-xs"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useToast } from 'vue-toastification'
import Button from 'primevue/button'
import { useNotificationsStore } from '@/stores/notifications'
import { getNotificationActions, getNotificationTypeColor, getNotificationTypeIcon } from '@/utils/notificationActions'
import type { NotificationResponse } from '@/api/api-reference'
import type { NotificationAction } from '@/utils/notificationActions'

interface Props {
  notification: NotificationResponse
}

const props = defineProps<Props>()
const emit = defineEmits<{
  deleted: [notificationId: string]
}>()

const toast = useToast()
const notificationsStore = useNotificationsStore()

const isUpdating = ref(false)
const isDeleting = ref(false)
const isActionLoading = ref(false)
const actionsProcessed = ref<string[]>([]) // track which actions have been processed

// get available actions for this notification, filtering out processed ones
const actions = computed(() => {
  const allActions = getNotificationActions(props.notification)
  // filter out actions that have been processed locally
  return allActions.filter(action => !actionsProcessed.value.includes(action.label))
})

async function toggleReadStatus() {
  if (!props.notification.id) return

  try {
    isUpdating.value = true

    if (props.notification.status === 'Unread') {
      await notificationsStore.markAsRead(props.notification.id)
      toast.success('Marked as read')
    } else {
      // note: there's no "mark as unread" API endpoint, so we'll just show a message
      toast.info('This notification is already read')
    }
  } catch (error) {
    toast.error('Failed to update notification status')
  } finally {
    isUpdating.value = false
  }
}

async function deleteNotification() {
  if (!props.notification.id) return

  try {
    isDeleting.value = true
    await notificationsStore.deleteNotification(props.notification.id)
    emit('deleted', props.notification.id)
    toast.success('Notification deleted')
  } catch (error) {
    toast.error('Failed to delete notification')
  } finally {
    isDeleting.value = false
  }
}

async function handleActionClick(action: NotificationAction) {
  try {
    isActionLoading.value = true
    await action.action()

    // mark this action as processed locally (immediate UI feedback)
    actionsProcessed.value.push(action.label)

    // mark as read after action
    if (props.notification.status === 'Unread' && props.notification.id) {
      await notificationsStore.markAsRead(props.notification.id)
    }

    // refresh notifications to get updated state from backend
    // this will eventually sync with backend changes
    setTimeout(async () => {
      await notificationsStore.fetchNotifications(1)
      await notificationsStore.fetchUnreadCount()
    }, 1000) // small delay to allow backend processing

  } catch (error) {
    // if action failed, remove from processed list
    actionsProcessed.value = actionsProcessed.value.filter(label => label !== action.label)
    console.error('Action failed:', error)
  } finally {
    isActionLoading.value = false
  }
}

function getButtonSeverity(variant: NotificationAction['variant']): string {
  switch (variant) {
    case 'primary':
      return 'primary'
    case 'secondary':
      return 'secondary'
    case 'success':
      return 'success'
    case 'danger':
      return 'danger'
    case 'warning':
      return 'warn'
    default:
      return 'secondary'
  }
}

function formatDate(dateString?: string): string {
  if (!dateString) return ''

  const date = new Date(dateString)
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

  if (diffInMinutes < 1) return 'Just now'
  if (diffInMinutes < 60) return `${diffInMinutes} minutes ago`

  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) return `${diffInHours} hours ago`

  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) return `${diffInDays} days ago`

  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>
