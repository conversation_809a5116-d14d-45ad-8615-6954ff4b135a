<template>
  <div class="relative">
    <button
      @click="toggleNotifications"
      class="relative p-2 text-white hover:text-gray-200 transition-colors duration-200"
      :class="{ 'text-yellow-300': unreadCount > 0 }"
    >
      <i class="pi pi-bell text-lg"></i>
      
      <!-- Unread count badge -->
      <span
        v-if="unreadCount > 0"
        class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium"
      >
        {{ unreadCount > 99 ? '99+' : unreadCount }}
      </span>
    </button>

    <!-- Notification dropdown -->
    <div
      v-if="isOpen"
      class="absolute right-0 top-full mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50"
    >
      <!-- Header -->
      <div class="px-4 py-3 border-b border-gray-200 flex items-center justify-between">
        <h3 class="text-sm font-semibold text-gray-900">Notifications</h3>
        <button
          @click="viewAllNotifications"
          class="text-xs text-blue-600 hover:text-blue-800 font-medium"
        >
          View All
        </button>
      </div>

      <!-- Notifications list -->
      <div class="max-h-96 overflow-y-auto">
        <div v-if="isLoading" class="p-4 text-center">
          <i class="pi pi-spinner pi-spin text-gray-400"></i>
          <p class="text-sm text-gray-500 mt-2">Loading notifications...</p>
        </div>

        <div v-else-if="notifications.length === 0" class="p-4 text-center">
          <i class="pi pi-bell-slash text-2xl text-gray-400 mb-2 block"></i>
          <p class="text-sm text-gray-500">No notifications</p>
        </div>

        <div v-else>
          <div
            v-for="notification in displayNotifications"
            :key="notification.id"
            class="px-4 py-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
            :class="{ 'bg-blue-50': notification.status === 'Unread' }"
            @click="handleNotificationClick(notification)"
          >
            <div class="flex items-start space-x-3">
              <!-- Notification icon -->
              <div
                class="flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center"
                :class="getNotificationTypeColor(notification.notificationType!)"
              >
                <i :class="getNotificationTypeIcon(notification.notificationType!)" class="text-sm"></i>
              </div>

              <!-- Notification content -->
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 truncate">
                  {{ notification.title }}
                </p>
                <p class="text-xs text-gray-500 mt-1 line-clamp-2">
                  {{ notification.message }}
                </p>
                <p class="text-xs text-gray-400 mt-1">
                  {{ formatDate(notification.createdAt) }}
                </p>
              </div>

              <!-- Unread indicator -->
              <div v-if="notification.status === 'Unread'" class="flex-shrink-0">
                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div v-if="notifications.length > 0" class="px-4 py-3 border-t border-gray-200">
        <button
          @click="viewAllNotifications"
          class="w-full text-center text-sm text-blue-600 hover:text-blue-800 font-medium"
        >
          View All Notifications
        </button>
      </div>
    </div>

    <!-- Backdrop -->
    <div
      v-if="isOpen"
      @click="closeNotifications"
      class="fixed inset-0 z-40"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { useNotificationsStore } from '@/stores/notifications'
import { getNotificationTypeColor, getNotificationTypeIcon } from '@/utils/notificationActions'
import type { NotificationResponse } from '@/api/api-reference'

const router = useRouter()
const notificationsStore = useNotificationsStore()

const isOpen = ref(false)

// computed properties from store
const notifications = computed(() => notificationsStore.notifications)
const unreadCount = computed(() => notificationsStore.unreadCount)
const isLoading = computed(() => notificationsStore.isLoading)

// show only first 5 notifications in dropdown
const displayNotifications = computed(() => notifications.value.slice(0, 5))

function toggleNotifications() {
  isOpen.value = !isOpen.value
  if (isOpen.value && notifications.value.length === 0) {
    notificationsStore.fetchNotifications()
  }
}

function closeNotifications() {
  isOpen.value = false
}

function viewAllNotifications() {
  router.push('/notifications')
  closeNotifications()
}

async function handleNotificationClick(notification: NotificationResponse) {
  // mark as read if unread
  if (notification.status === 'Unread' && notification.id) {
    await notificationsStore.markAsRead(notification.id)
  }
  
  // close dropdown
  closeNotifications()
  
  // navigate to notifications page to show actions
  router.push('/notifications')
}

function formatDate(dateString?: string): string {
  if (!dateString) return ''
  
  const date = new Date(dateString)
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
  
  if (diffInMinutes < 1) return 'Just now'
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`
  
  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) return `${diffInHours}h ago`
  
  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) return `${diffInDays}d ago`
  
  return date.toLocaleDateString()
}

// handle click outside
function handleClickOutside(event: MouseEvent) {
  const target = event.target as Element
  if (!target.closest('.relative')) {
    closeNotifications()
  }
}

onMounted(() => {
  // start polling for notifications
  notificationsStore.startPolling()
  notificationsStore.fetchUnreadCount()
  
  document.addEventListener('click', handleClickOutside)
})

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
