<template>
  <nav class="bg-primary text-white">
    <div class="mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16 items-center border-b border-transparent">
        <!-- Left Section: Navigation Items -->
        <div class="flex items-center">
          <button class="lg:hidden mr-2" @click="isMobileMenuOpen = !isMobileMenuOpen">
            <!-- Mobile Menu Button -->
            <svg
              class="h-6 w-6"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 6h16M4 12h16m-7 6h7"
              />
            </svg>
          </button>
          <div class="hidden lg:flex space-x-4">
            <div v-for="item in navItems" :key="item.name">
              <router-link
                v-if="item.route && !item.subitems"
                :to="item.route"
                class="relative px-3 py-5 border-b-4"
                :class="{
                  'border-white': isActiveRoute(item.route),
                  'border-transparent hover:border-white': !isActiveRoute(item.route),
                }"
              >
                <span>{{ item.name }}</span>
              </router-link>
              <div v-if="item.subitems" class="relative nav-dropdown">
                <button @click="toggleItemDropdown(item)" class="flex items-center space-x-2">
                  <span>{{ item.name }}</span>
                  <ChevronDownIcon class="h-4 w-4"/>
                </button>
                <div
                  v-if="item.isOpen"
                  class="absolute right-0 mt-2 w-48 bg-white text-black rounded-md shadow-lg z-30"
                >
                  <div v-for="subItem in item.subitems" :key="subItem.name">
                    <a
                      v-if="subItem.route && !subItem.subitems"
                      :href="subItem.route"
                      class="block px-4 py-2 hover:bg-blue-100"
                    >
                      {{ subItem.name }}
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="flex relative nav-dropdown gap-4 items-center">
          <!-- Notifications Icon -->
          <NotificationIcon />

          <!-- Location Dropdown -->
          <button
            v-if="locationName"
            @click="toggleLocationDropdown"
            class="flex items-center space-x-2"
            aria-haspopup="true"
            :aria-expanded="isLocationDropdownOpen"
          >
            <span>{{ locationName }}</span>
            <ChevronDownIcon v-if="multipleLocations" class="h-4 w-4"/>
          </button>

          <ul
            v-if="isLocationDropdownOpen && multipleLocations"
            class="absolute left-0 top-full mt-2 min-w-[10rem] bg-white text-black rounded-md shadow-lg z-30"
          >
            <li v-for="location in authStore?.user?.availableLocations" :key="location.locationId">
              <a
                href="#"
                @click.prevent="switchLocation(location.locationId!, location.organizationId!)"
                class="block px-4 py-2 hover:bg-blue-100"
              >
                {{ location.locationName }} - {{ location.organizationName }}
              </a>
            </li>
          </ul>

          <!-- User Dropdown -->
          <button
            @click="toggleUserDropdown"
            class="flex items-center space-x-2"
            aria-haspopup="true"
            :aria-expanded="isUserDropdownOpen"
          >
            <span>{{ fullName }}</span>
            <ChevronDownIcon class="h-4 w-4"/>
          </button>

          <ul
            v-if="isUserDropdownOpen"
            class="absolute right-0 top-full mt-2 min-w-[10rem] bg-white text-black rounded-md shadow-lg z-30"
          >
            <li v-if="employeeWithPatientProfile">
              <a
                href="#"
                @click.prevent="switchToPatient"
                class="block px-4 py-2 hover:bg-blue-100"
              >
                Switch to Patient
              </a>
            </li>
            <li v-if="patientWitEmployeeProfile">
              <a
                href="#"
                @click.prevent="switchToEmployee"
                class="block px-4 py-2 hover:bg-blue-100"
              >
                Switch to Employee
              </a>
            </li>
            <li v-if="isPractitioner">
              <a href="#" @click.prevent="profile" class="block px-4 py-2 hover:bg-blue-100">
                Profile
              </a>
            </li>
            <li>
              <a href="#" @click.prevent="logout" class="block px-4 py-2 hover:bg-blue-100">
                Logout
              </a>
            </li>
          </ul>
        </div>
      </div>

      <!-- Mobile Menu -->
      <div v-if="isMobileMenuOpen" class="sm:hidden">
        <div class="space-y-1 px-2 pt-2 pb-3">
          <div v-for="item in navItems" :key="item.name">
            <router-link
              v-if="item.route"
              :to="item.route"
              class="block px-3 py-2 border-b-4"
              :class="{
                'border-white': isActiveRoute(item.route),
                'border-transparent hover:border-white': !isActiveRoute(item.route),
              }"
            >
              <span>{{ item.name }}</span>
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import {ref, onMounted, onBeforeUnmount, computed, watch} from 'vue'
import {useRoute} from 'vue-router'
import {ChevronDownIcon} from '@heroicons/vue/24/outline'
import {useAuthStore} from '@/stores/auth'
import NotificationIcon from '@/components/notifications/NotificationIcon.vue'
import router from '../../router'

const authStore = useAuthStore()

const isMobileMenuOpen = ref(false)
const isUserDropdownOpen = ref(false)
const isLocationDropdownOpen = ref(false)

const fullName = computed(() => `${authStore.user?.firstName} ${authStore.user?.lastName}`)
const locationName = computed(() =>
  authStore.user?.selectedUserRole == 'Employee'
    ? `${authStore.user?.locationName} - ${authStore.user?.organizationName}`
    : null,
)
const multipleLocations = computed(
  () => authStore.user?.availableLocations && authStore.user?.availableLocations?.length > 0,
)
const isPractitioner = computed(() =>
  authStore.user?.locationEmployeeRoles?.includes('Practitioner'),
)

const employeeWithPatientProfile = computed(
  () => authStore.user?.selectedUserRole == 'Employee' && authStore.user?.patientId,
)

const patientWitEmployeeProfile = computed(
  () => authStore.user?.selectedUserRole == 'Patient' && authStore.user?.employeeId,
)

interface NavItem {
  name: string
  route?: string
  subitems?: NavItem[]
  isOpen?: boolean
}

const navItems = ref<NavItem[]>([])

const setNavigation = () => {
  if (authStore.user?.selectedUserRole == 'Patient') {
    navItems.value = [
      {name: 'Appointments', route: '/patient-appointments'},
      {name: 'Records', route: '/my-records'},
      {name: 'Profile', route: '/patient-profile/personal-info'},
      {name: 'Questionnaires', route: '/patient/questionnaires'},
    ]
  } else if (authStore.user?.selectedUserRole == 'Employee') {
    navItems.value = [
      {name: 'Dashboard', route: '/dashboard'},
      {name: 'Appointments', route: '/appointments'},
      {name: 'Patients', route: '/patients'},
      {name: 'Notes', route: '/encounters'},
      {name: 'Payments', route: '/payments'},
      {
        name: 'Templates',
        isOpen: false,
        subitems: [{name: 'Order Bundles', route: '/order-bundle-templates'}, {
          name: 'Note Templates',
          route: '/note-templates'
        }],
      },
    ]
    if (authStore.user?.locationEmployeeRoles?.includes('OrganizationAdmin')) {
      navItems.value.push({
        name: 'Admin',
        isOpen: false,
        subitems: [
          {name: 'Employees', route: '/employees'},
          {name: 'Locations', route: '/locations'},
        ],
      })
    }
    if (authStore.user?.locationEmployeeRoles?.includes('LocationAdmin')) {
      navItems.value.push({
        name: 'Admin',
        isOpen: false,
        subitems: [
          {name: 'Employees', route: '/employees'},
          {name: 'Questionnaires', route: '/questionnaires'},
        ],
      })
    }
  }
  if (authStore.user?.selectedUserRole == 'SuperAdmin') {
    navItems.value = [{name: 'Organization', route: '/organizations'}]
  }
  if (!authStore.user?.patientId) {
    navItems.value.push({
      name: 'Resources',
      isOpen: false,
      subitems: [
        {name: 'Medications', route: '/medications'},
        {name: 'Snomed', route: '/snomed'},
        {name: 'Allergies', route: '/allergies'},
        {name: 'Immunizations', route: '/immunizations'},
        {name: 'ICD 10', route: '/icd10'},
        {name: 'CPT Codes', route: '/cptcodes'},
        {name: 'Loinc Codes', route: '/loinccodes'},
      ],
    })
  }
}

if (authStore.user) {
  setNavigation()
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

watch(
  () => authStore.user,
  () => {
    setNavigation()
  },
)

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside)
})

const route = useRoute()

const toggleUserDropdown = () => {
  isUserDropdownOpen.value = !isUserDropdownOpen.value
  isLocationDropdownOpen.value = false
  closeNavDropdowns()
}

const toggleLocationDropdown = () => {
  isLocationDropdownOpen.value = !isLocationDropdownOpen.value
  isUserDropdownOpen.value = false
  closeNavDropdowns()
}

const toggleItemDropdown = (item: NavItem) => {
  const otherNavItems = navItems.value.filter((x) => x.name != item.name)
  otherNavItems.forEach((item) => {
    item.isOpen = false
  })
  closeUserDropdown()
  item.isOpen = !item.isOpen
}

const isActiveRoute = (path: string) => {
  return route.path === path
}

// const isActiveSubRoute = (items: NavItem[]) => {
//   return items.some((x) => x.route == route.path)
// }

const logout = () => {
  authStore.logout()
}

const profile = () => {
  router.push('/practitioner-profile')
  closeUserDropdown()
}

const closeUserDropdown = () => {
  isUserDropdownOpen.value = false
  isLocationDropdownOpen.value = false
}

const closeNavDropdowns = () => {
  navItems.value.forEach((item) => {
    item.isOpen = false
  })
}

const handleClickOutside = (event: MouseEvent) => {
  const clickedElement = event.target
  if (!hasParentClass(clickedElement, 'nav-dropdown')) {
    closeNavDropdowns()
    closeUserDropdown()
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const hasParentClass = (element: any, className: string) => {
  let currentElement = element

  while (currentElement) {
    if (currentElement.classList.contains(className)) {
      return true
    }
    currentElement = currentElement.parentElement
  }

  return false
}

const switchLocation = async (locationId: string, organizationId: string) => {
  await authStore.updateSession({
    selectedLocationId: locationId,
    selectedOrganizationId: organizationId,
    selectedUserRole: 'Employee',
  })
  router.go(0)
}

const switchToPatient = async () => {
  await authStore.updateSession({
    selectedUserRole: 'Patient',
  })
  await authStore.getUser()
  isUserDropdownOpen.value = false
  router.push('/my-appointments')
}

const switchToEmployee = async () => {
  await authStore.updateSession({
    selectedUserRole: 'Employee',
  })
  await authStore.getUser()
  isUserDropdownOpen.value = false
  router.push('/dashboard')
}
</script>
