<template>
  <div class="min-h-screen bg-gray-50 py-6">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Header -->
      <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900">Notifications</h1>
        <p class="text-sm text-gray-600 mt-1">
          Stay updated with your latest notifications
        </p>
      </div>

      <!-- Stats -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div class="bg-white rounded-lg border border-gray-200 p-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="pi pi-bell text-blue-600 text-xl"></i>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-500">Total</p>
              <p class="text-lg font-semibold text-gray-900">{{ totalItems }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg border border-gray-200 p-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="pi pi-circle-fill text-blue-600 text-xl"></i>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-500">Unread</p>
              <p class="text-lg font-semibold text-gray-900">{{ unreadCount }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg border border-gray-200 p-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="pi pi-check-circle text-green-600 text-xl"></i>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-500">Read</p>
              <p class="text-lg font-semibold text-gray-900">{{ totalItems - unreadCount }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Filters -->
      <div class="bg-white rounded-lg border border-gray-200 p-4 mb-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div class="flex items-center space-x-4">
            <label class="text-sm font-medium text-gray-700">Filter by status:</label>
            <Select
              v-model="selectedStatus"
              :options="statusOptions"
              optionLabel="label"
              optionValue="value"
              placeholder="All notifications"
              class="w-48"
              @change="applyFilters"
            />
          </div>

          <div class="flex items-center space-x-2">
            <Button
              label="Mark All as Read"
              icon="pi pi-check"
              size="small"
              severity="secondary"
              @click="markAllAsRead"
              :disabled="unreadCount === 0 || isMarkingAllRead"
              :loading="isMarkingAllRead"
            />
            
            <Button
              label="Refresh"
              icon="pi pi-refresh"
              size="small"
              severity="secondary"
              @click="refreshNotifications"
              :loading="isLoading"
            />
          </div>
        </div>
      </div>

      <!-- Notifications list -->
      <div class="space-y-4">
        <!-- Loading state -->
        <div v-if="isLoading && notifications.length === 0" class="text-center py-12">
          <i class="pi pi-spinner pi-spin text-gray-400 text-2xl mb-4 block"></i>
          <p class="text-gray-500">Loading notifications...</p>
        </div>

        <!-- Empty state -->
        <div v-else-if="filteredNotifications.length === 0" class="text-center py-12">
          <i class="pi pi-bell-slash text-gray-400 text-4xl mb-4 block"></i>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No notifications</h3>
          <p class="text-gray-500">
            {{ selectedStatus ? 'No notifications match your filter.' : 'You\'re all caught up!' }}
          </p>
        </div>

        <!-- Notifications -->
        <div v-else>
          <NotificationItem
            v-for="notification in filteredNotifications"
            :key="notification.id"
            :notification="notification"
            @deleted="handleNotificationDeleted"
          />
        </div>
      </div>

      <!-- Load more button -->
      <div v-if="hasMore && !isLoading" class="text-center mt-8">
        <Button
          label="Load More"
          icon="pi pi-chevron-down"
          severity="secondary"
          @click="loadMore"
          :loading="isLoadingMore"
        />
      </div>

      <!-- Loading more indicator -->
      <div v-if="isLoadingMore" class="text-center py-4">
        <i class="pi pi-spinner pi-spin text-gray-400"></i>
        <p class="text-sm text-gray-500 mt-2">Loading more notifications...</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useToast } from 'vue-toastification'
import Button from 'primevue/button'
import Select from 'primevue/select'
import { useNotificationsStore } from '@/stores/notifications'
import NotificationItem from '@/components/notifications/NotificationItem.vue'

const toast = useToast()
const notificationsStore = useNotificationsStore()

const selectedStatus = ref<string | null>(null)
const isMarkingAllRead = ref(false)
const isLoadingMore = ref(false)

const statusOptions = [
  { label: 'All notifications', value: null },
  { label: 'Unread only', value: 'Unread' },
  { label: 'Read only', value: 'Read' }
]

// computed properties from store
const notifications = computed(() => notificationsStore.notifications)
const unreadCount = computed(() => notificationsStore.unreadCount)
const isLoading = computed(() => notificationsStore.isLoading)
const currentPage = computed(() => notificationsStore.currentPage)
const totalPages = computed(() => notificationsStore.totalPages)
const totalItems = computed(() => notificationsStore.totalItems)

const hasMore = computed(() => currentPage.value < totalPages.value)

const filteredNotifications = computed(() => {
  if (!selectedStatus.value) {
    return notifications.value
  }
  return notifications.value.filter(n => n.status === selectedStatus.value)
})

async function refreshNotifications() {
  await notificationsStore.fetchNotifications(1)
  await notificationsStore.fetchUnreadCount()
}

async function loadMore() {
  isLoadingMore.value = true
  try {
    await notificationsStore.loadMore()
  } finally {
    isLoadingMore.value = false
  }
}

async function markAllAsRead() {
  isMarkingAllRead.value = true
  try {
    const unreadNotifications = notifications.value.filter(n => n.status === 'Unread')
    
    // mark all unread notifications as read
    for (const notification of unreadNotifications) {
      if (notification.id) {
        await notificationsStore.markAsRead(notification.id)
      }
    }
    
    toast.success('All notifications marked as read')
  } catch (error) {
    toast.error('Failed to mark all notifications as read')
  } finally {
    isMarkingAllRead.value = false
  }
}

function applyFilters() {
  // filters are applied via computed property
}

function handleNotificationDeleted(notificationId: string) {
  // notification is already removed from store by the NotificationItem component
}

onMounted(async () => {
  // fetch notifications and start polling
  await refreshNotifications()
  notificationsStore.startPolling()
})

onBeforeUnmount(() => {
  // stop polling when leaving the page
  notificationsStore.stopPolling()
})
</script>
